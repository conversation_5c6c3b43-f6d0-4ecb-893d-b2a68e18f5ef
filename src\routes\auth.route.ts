import express from 'express'
import { authAPI } from '@/api'
import { Request, Response } from 'express';

// Authenticated router
const authRouter = express.Router()

authRouter.post('/login', authAPI.loginHandler);
authRouter.get('/authen', authAPI.authenticateHandler);
authRouter.get('/logout/:id', authAPI.logoutHandler);
authRouter.get('/refresh-token/:user_id', authAPI.refreshTokenHandler);

// Error Route
authRouter.get('/error', (req: Request, res: Response) => {
  res.status(400).json({ success: false, message: 'Authentication failed' });
});

// Export router
const router = express.Router()
router.use('/auth', authRouter)

export default router
