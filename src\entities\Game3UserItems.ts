import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { Location } from './Location'
import { ItemsGame } from './ItemsGame'

@Entity('game3_user_items', { schema: 'events' })
@Index(['userId', 'gameId', 'locationId', 'itemId'], { unique: true })
export class Game3UserItems extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('int', { name: 'item_id'})
  itemId: number

  @Column('int', { name: 'quantity', default: 0 })
  quantity: number

  @Column('int', { name: 'used', default: 0 })
  used: number

  @ManyToOne(() => TblUsers)
  @JoinColumn({ name: 'user_id' })
  user: TblUsers

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location

  @ManyToOne(() => ItemsGame)
  @JoinColumn({ name: 'item_id' })
  itemsGame: ItemsGame
}
