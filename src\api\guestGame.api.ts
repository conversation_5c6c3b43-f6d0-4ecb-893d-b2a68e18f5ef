import express from 'express'
import { getPrizesWithRemainingQuantity, randomPrizeWithQuantityCheck, selectPrizeByWinrate } from '@/services/gameG1.service'
import { AppDataSource } from '@/config/config'
import { GuestGameSession, TblGames, SpinHistory } from '@/entities'
import { NotFoundError } from '@/utils/ApiError'
import { v4 as uuidv4 } from 'uuid'
import { luckyWheelService } from '@/services'
import { CommonService } from '@/services/common.service'

// Helper functions
const guestGameSessionRepo = () => AppDataSource.getRepository(GuestGameSession)
const gameRepo = () => AppDataSource.getRepository(TblGames)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return new Date(new Date().getTime() + 7 * 60 * 60 * 1000)
}

// Xử lý Game G1 cho guest
const processGuestG1Game = async (
  guestSession: GuestGameSession,
  game: any,
  prizes: any[],
  context: string
) => {
  let prize: any
  
  // Guest user luôn được coi là lần đầu chơi - bắt buộc trúng voucher100
  const voucher100Prizes = await luckyWheelService.getActivePrizesVoucher100(
    guestSession.campaignId, 
    guestSession.gameId, 
    context
  )

  if (voucher100Prizes.length > 0) {
    // Tạo map để kiểm tra số lượng đã trúng cho voucher100
    const voucher100PrizeIds = voucher100Prizes.map(p => p.id)
    const voucher100WonCountMap = new Map<number, number>()

    if (voucher100PrizeIds.length > 0) {
      const spinHistoryRepo = AppDataSource.getRepository(SpinHistory)
      
      const wonCounts = await spinHistoryRepo
        .createQueryBuilder('spin_history')
        .select('spin_history.prizeId', 'prizeId')
        .addSelect('COUNT(*)', 'count')
        .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucher100PrizeIds })
        .groupBy('spin_history.prizeId')
        .getRawMany()

      wonCounts.forEach(item => {
        voucher100WonCountMap.set(parseInt(item.prizeId), parseInt(item.count))
      })
    }

    const availableVoucher100 = voucher100Prizes
      .map(v100Prize => {
        const wonCount = voucher100WonCountMap.get(v100Prize.id) || 0
        const remainingQuantity = v100Prize.quantity - wonCount
        return {
          ...v100Prize,
          remainingQuantity
        }
      })
      .filter(p => p.remainingQuantity > 0)

    if (availableVoucher100.length > 0) {
      // Random chọn 1 voucher100 từ danh sách còn số lượng
      const randomIndex = Math.floor(Math.random() * availableVoucher100.length)
      prize = availableVoucher100[randomIndex]
    } else {
      // Nếu voucher100 hết số lượng, dùng logic random như cũ
      prize = randomPrizeWithQuantityCheck(prizes)
    }
  } else {
    // Nếu không có voucher100, dùng logic random như cũ
    prize = randomPrizeWithQuantityCheck(prizes)
  }
  
  if (!prize) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  // Cập nhật guest session
  guestSession.hasPlayed = true
  guestSession.prizesWon = [{
    id: prize.id,
    name: prize.name,
    type: prize.type,
    bizStorageId: prize.bizStorageId,
    wonAt: getNowGMT7()
  }]
  guestSession.updatedAt = getNowGMT7()

  await guestGameSessionRepo().save(guestSession)

  return {
    status: 'success',
    message: `Bạn đã nhận được ${prize.name}! Đăng nhập để nhận phần thưởng.`,
    guest_session_id: guestSession.guestSessionId,
    play_turns: 0, // Đã hết lượt
    prize: {
      id: prize.id,
      name: prize.name,
      type: prize.type
    }
  }
}

// Xử lý Game G2 cho guest
const processGuestG2Game = async (
  guestSession: GuestGameSession,
  game: any,
  prizes: any[],
  listBoxId: number[] = []
) => {
  const config = {
    MAX_WINNING_BOXES: game.maxWinPrize || 0,
  }

  const winPrizes = prizes.filter(p => p.type !== 'lose')
  const losePrizes = prizes.filter(p => p.type === 'lose')

  if (winPrizes.length === 0 && losePrizes.length === 0) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  const boxes = Array(9).fill(null)
  const sentBoxIds = listBoxId.length > 0 ? listBoxId : []

  let actualWinningBoxCount = 0
  let predefinedWinningPrize = null

  if (config.MAX_WINNING_BOXES === 0) {
    const randomizedPrize = randomPrizeWithQuantityCheck(prizes)
    if (randomizedPrize && ['voucher', 'spin'].includes(randomizedPrize.type)) {
      actualWinningBoxCount = Math.min(1, sentBoxIds.length)
    }
    predefinedWinningPrize = randomizedPrize
  } else {
    actualWinningBoxCount = Math.min(config.MAX_WINNING_BOXES, sentBoxIds.length)
  }

  const winningBoxes = []
  if (actualWinningBoxCount > 0) {
    const remainingSentBoxes = [...sentBoxIds]
    for (let i = 0; i < actualWinningBoxCount; i++) {
      const randomIndex = Math.floor(Math.random() * remainingSentBoxes.length)
      winningBoxes.push(remainingSentBoxes.splice(randomIndex, 1)[0])
    }
  }

  const usedPrizeIds = new Set()
  const wonPrizes = []

  // Fill các box được gửi lên
  for (const boxId of sentBoxIds) {
    const boxIndex = boxId - 1
    if (winningBoxes.includes(boxId)) {
      let prize: any
      if (predefinedWinningPrize && winningBoxes[0] === boxId) {
        prize = predefinedWinningPrize
      } else {
        const availableWinPrizes = winPrizes.filter(p =>
          !usedPrizeIds.has(p.id) && p.remainingQuantity > 0
        )
        const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes
        prize = selectPrizeByWinrate(prizeToUse)
      }

      boxes[boxIndex] = {
        id: boxId,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        }
      }

      wonPrizes.push({
        id: prize.id,
        name: prize.name,
        type: prize.type,
        bizStorageId: prize.bizStorageId,
        wonAt: getNowGMT7()
      })

      usedPrizeIds.add(prize.id)
    } else {
      if (losePrizes.length > 0) {
        const availableLosePrizes = losePrizes.filter(p => !usedPrizeIds.has(p.id))
        const prizeToUse = availableLosePrizes.length > 0 ? availableLosePrizes : losePrizes
        const prizeIndex = Math.floor(Math.random() * prizeToUse.length)
        const prize = prizeToUse[prizeIndex]

        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: prize.id,
            name: prize.name,
            image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
          }
        }
        usedPrizeIds.add(prize.id)
      } else {
        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: 0,
            name: 'Chúc bạn may mắn lần sau',
            image: ''
          }
        }
      }
    }
  }

  // Fill các box còn lại
  const allPrizes = [...winPrizes, ...losePrizes]
  const availablePrizesForRemaining = allPrizes.filter(p => !usedPrizeIds.has(p.id))
  const prizesToUseForRemaining = availablePrizesForRemaining.length > 0 ? availablePrizesForRemaining : allPrizes

  const shufflePrizes = (array: typeof allPrizes) => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  const shuffledPrizes = shufflePrizes(prizesToUseForRemaining)
  let remainingPrizeIndex = 0

  for (let i = 1; i <= 9; i++) {
    if (boxes[i - 1] === null) {
      const prize = shuffledPrizes[remainingPrizeIndex % shuffledPrizes.length]

      boxes[i - 1] = {
        id: i,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        }
      }
      remainingPrizeIndex++
    }
  }

  // Tạo array các box đã mở với reward tương ứng
  const openedBoxData = sentBoxIds.map(boxId => {
    const box = boxes.find(b => b.id === boxId)

    return {
      box_id: boxId,
      reward: {
        id: box?.reward?.reward_id || null,
        name: box?.reward?.name || "Chúc bạn may mắn lần sau!",
        image: box?.reward?.image || "chuc_mm",
        type: wonPrizes.find(p => p.id === box?.reward?.reward_id)?.type || "lose",
        has_voucher: winningBoxes.includes(boxId)
      },
      opened_at: getNowGMT7()
    }
  })

  // Cập nhật guest session
  guestSession.hasPlayed = true
  guestSession.prizesWon = wonPrizes
  guestSession.openedBoxes = openedBoxData
  guestSession.updatedAt = getNowGMT7()

  await guestGameSessionRepo().save(guestSession)

  let message = 'Chúc bạn may mắn lần sau!'
  if (wonPrizes.length > 0) {
    const boxNumbers = winningBoxes.join(', ')
    message = `Bạn đã mở hộp quà số ${boxNumbers}! Đăng nhập để nhận phần thưởng.`
  }

  return {
    status: 'success',
    message,
    guest_session_id: guestSession.guestSessionId,
    boxes,
    play_turns: 0 // Đã hết lượt
  }
}

/**
 * GET /api/guest-game/:slug/init
 * Initialize game cho guest user chưa đăng nhập
 */
const initGuestGameHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { slug } = req.params
    
    if (!slug) {
      return res.status(200).json({
        status: 'error',
        message: 'Missing game slug'
      })
    }

    const gameId = CommonService.convertGameIdSlugToNumber(slug)
    if (!gameId) {
      return res.status(200).json({
        status: 'error',
        message: 'Invalid game slug'
      })
    }

    // Lấy thông tin game để có campaignId
    const game = await gameRepo().findOne({
      where: { id: gameId }
    })

    if (!game) {
      return res.status(200).json({
        status: 'error',
        message: 'Game không tồn tại'
      })
    }

    // Tạo guest session mới
    const guestSessionId = uuidv4()
    const now = getNowGMT7()
    
    const guestSession = new GuestGameSession()
    guestSession.guestSessionId = guestSessionId
    guestSession.campaignId = game.campaignId
    guestSession.gameId = gameId
    guestSession.prizesWon = []
    guestSession.hasPlayed = false
    guestSession.isMerged = false
    guestSession.mergedUserId = null
    guestSession.createdAt = now
    guestSession.updatedAt = now
    
    await guestGameSessionRepo().save(guestSession)
    
    return res.status(200).json({
      status: 'success',
      guest_session_id: guestSessionId,
      play_turns: 1 // Guest chỉ được chơi 1 lần
    })
  } catch (error) {
    return res.status(200).json({
      status: 'error',
      message: error.message
    })
  }
}

/**
 * POST /api/guest-game/:slug/claim
 * Claim voucher cho guest user
 */
const claimGuestVoucherHandler = async (req: express.Request, res: express.Response) => {
  try {
    const { slug } = req.params
    const { guest_session_id: guestSessionId, box_ids: boxIds } = req.body
    
    if (!slug) {
      return res.status(200).json({
        status: 'error',
        message: 'Missing game slug'
      })
    }

    if (!guestSessionId) {
      return res.status(200).json({
        status: 'error',
        message: 'Missing guest_session_id'
      })
    }

    const gameId = CommonService.convertGameIdSlugToNumber(slug)
    if (!gameId) {
      return res.status(200).json({
        status: 'error',
        message: 'Invalid game slug'
      })
    }

    // Lấy thông tin guest session
    const guestSession = await guestGameSessionRepo().findOne({
      where: { guestSessionId, gameId, isMerged: false }
    })

    if (!guestSession) {
      return res.status(200).json({
        status: 'error',
        message: 'Guest session không tồn tại hoặc đã được merge'
      })
    }

    // Kiểm tra đã chơi chưa
    if (guestSession.hasPlayed) {
      return res.status(200).json({
        status: 'no_turns',
        message: 'Bạn đã chơi rồi. Đăng nhập để chơi thêm!',
        guest_session_id: guestSessionId
      })
    }

    // Lấy thông tin game
    const game = await gameRepo().findOne({
      where: { id: gameId }
    })

    if (!game) {
      return res.status(200).json({
        status: 'error',
        message: 'Game không tồn tại'
      })
    }

    // Lấy prizes với context fix trong code
    const context = "10";
    // Lấy prizes với context fix trong code
    const prizes = await getPrizesWithRemainingQuantity(
      guestSession.campaignId, 
      gameId, 
      context // Context được fix trong code
    )

    if (prizes.length === 0) {
      return res.status(200).json({
        status: 'error',
        message: 'Không có phần thưởng nào'
      })
    }

    let result: any

    if (slug === 'g1') {
      result = await processGuestG1Game(guestSession, game, prizes, context)
    } else if (slug === 'g2') {
      result = await processGuestG2Game(guestSession, game, prizes, boxIds || [])
    } else {
      return res.status(200).json({
        status: 'error',
        message: 'Game không được hỗ trợ cho guest user'
      })
    }

    return res.status(200).json(result)
  } catch (error) {
    return res.status(200).json({
      status: 'error',
      message: error.message
    })
  }
}

export { 
  initGuestGameHandler,
  claimGuestVoucherHandler
} 