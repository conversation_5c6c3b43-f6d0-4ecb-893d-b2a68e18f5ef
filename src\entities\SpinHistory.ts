import { <PERSON>ti<PERSON>, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm'
import { LuckyPrize } from './LuckyPrize'

@Entity('spin_histories')
export class SpinHistory {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('bigint', { name: 'user_id' })
  userId: number

  @Column('bigint', { name: 'prize_id' })
  prizeId: number

  @ManyToOne(() => LuckyPrize)
  @JoinColumn({ name: 'prize_id' })
  prize: LuckyPrize

  @Column('timestamp', { name: 'spin_time' })
  spinTime: Date

  @Column('text', { name: 'description', nullable: true })
  description: string

  @Column('text', { name: 'address', nullable: true })
  address: string

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'claimed_at', nullable: true })
  claimedAt: Date | null

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null

  @Column('varchar', { name: 'status', length: 255 })
  status: string | 'Chưa sử dụng'

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('int', { name: 'biz_coupon_id', nullable: true })
  bizCouponId: number | null

  @Column('text', { name: 'voucher_code', nullable: true })
  voucherCode: string | null

  @Column('text', { name: 'voucher_name', nullable: true })
  voucherName: string | null

  @Column('text', { name: 'voucher_link', nullable: true })
  voucherLink: string | null

  @Column('varchar', {name: 'voucher_expried', length: 255, nullable: true})
  voucherExpried: string | null
} 