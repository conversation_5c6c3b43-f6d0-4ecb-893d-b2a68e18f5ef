import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { Location } from './Location'
import { ItemsGame } from './ItemsGame'

@Entity('location_requirements', { schema: 'events' })
export class LocationRequirement extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('int', { name: 'game_id', nullable: true })
  gameId: number | null

  @Column('int', { name: 'item_id' })
  itemId: number

  @Column('int', { name: 'required_quantity' })
  requiredQuantity: number

  @Column('int', { name: 'winrate', nullable: true })
  winrate: number | null

  @Column('int', { name: 'max', nullable: true })
  max: number | null

  @Column('int', { name: 'min', nullable: true })
  min: number | null

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location

  @ManyToOne(() => ItemsGame)
  @JoinColumn({ name: 'item_id' })
  item: ItemsGame

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date
}
