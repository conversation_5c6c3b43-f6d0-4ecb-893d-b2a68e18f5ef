# 🎮 Tokyo Life Game G1 - API Usage Guide

## 🔐 API cho User đã đăng nhập (Authenticated)

### 1. 🚀 **Initialize Game**

Khởi tạo game, preload user data và game UI. User sẽ nhận thêm 1 lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": "25442",
        "name": "Nguyễn Đạt"
    },
    "play_turns": 1
}
```

### 3. 🎁 **Claim Voucher**

**Request Body:**
```json
{
  "voucher_id": 2
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
    "context": "1"
}'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn nhận được Voucher giảm giá 10%",
    "play_turns": 0
}
```

**Response Success (Hết voucher):**
```json
{
  "status": "success",
  "message": "Chúc bạn may mắn lần sau",
  "play_turns": 2
}
```

**Response (Hết lượt chơi):**
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "received_rewards": [
    {
      "id": 1,
      "name": "Voucher 50K"
    },
    {
      "id": 2, 
      "name": "Voucher 100K"
    }
  ],
  "share_remaining": 1
}
```

### 4. 📤 **Share for Extra Turn**


**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g1/share' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
    "platform": "facebook"
}'
```

**Response Success:**
```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 3
}
```

**Response (Đã share hôm nay):**
```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

**Response (Platform không hỗ trợ):**
```json
{
  "status": "error",
  "message": "Không hỗ trợ platform này"
}
```

## 🚨 Error Handling

### Common Error Responses:

**Missing Parameters:**
```json
{
  "status": "error",
  "message": "Missing game slug"
}
```

**Authentication Error:**
```json
{
  "status": "error",
  "message": "Unauthorized"
}
```

---

## 🌐 API cho Guest User (Chưa đăng nhập)

Guest user có thể chơi game mà không cần đăng nhập. Dữ liệu sẽ được lưu tạm thời và có thể merge vào tài khoản user khi đăng nhập.

### 1. 🚀 **Initialize Guest Game G1**

Khởi tạo session game cho guest user. Mỗi guest chỉ được chơi 1 lần.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g1/init'
```

**Response Success:**
```json
{
    "status": "success",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
    "play_turns": 1
}
```

### 2. 🎁 **Claim Guest Voucher G1**

Guest user claim voucher với session ID đã nhận được từ init.

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g1/claim' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}'
```

**Response Success (Lần đầu chơi):**
```json
{
    "status": "success",
    "message": "Bạn đã nhận được Voucher 100K! Đăng nhập để nhận phần thưởng.",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
    "play_turns": 0,
    "prize": {
        "id": 39,
        "name": "Voucher 100K",
        "type": "voucher"
    }
}
```

**Response (Đã chơi rồi):**
```json
{
    "status": "error",
    "message": "Guest đã chơi rồi. Mỗi guest chỉ được chơi 1 lần."
}
```

**Response Error:**
```json
{
    "status": "error",
    "message": "Guest session không tồn tại hoặc đã được merge"
}
```

### 3. 🔄 **Merge Guest Data**

Khi user đăng nhập, merge dữ liệu guest vào tài khoản user để nhận coupon thật.

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/merge-guest-data' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Đã merge dữ liệu guest thành công",
    "data": {
        "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
        "user_id": 25442,
        "processed_coupons": [
            {
                "prize": {
                    "id": 39,
                    "name": "Voucher 100K",
                    "type": "voucher",
                    "bizStorageId": "12345"
                },
                "coupon": {
                    "couponId": 67890,
                    "couponCode": "VC100K-ABC123",
                    "couponName": "Voucher 100K Tokyo Life",
                    "qrCodeLink": "https://bizfly.vn/qr/VC100K-ABC123",
                    "end_date": "2024-12-31"
                },
                "success": true
            }
        ],
        "errors": []
    }
}
```

**Response Error (Đã merge rồi):**
```json
{
    "status": "error",
    "message": "User đã được merge dữ liệu guest trong game này rồi"
}
```

### 🔍 **Guest Game Flow:**

1. **Guest chưa đăng nhập**: 
   - `GET /api/guest-game/g1/init` → Nhận `guest_session_id`
   - `POST /api/guest-game/g1/claim` → Chơi và nhận prize (lưu tạm thời)

2. **User đăng nhập**: 
   - `POST /api/game/merge-guest-data` → Merge dữ liệu guest + nhận coupon thật từ Bizfly

3. **Lưu ý quan trọng**:
   - Guest chỉ chơi được 1 lần duy nhất
   - Mỗi user chỉ merge được 1 lần trong cùng 1 game
   - Prize của guest được lưu tạm thời, chỉ nhận coupon thật sau khi merge
   - Guest luôn trúng voucher100 ở lần chơi đầu tiên (tương tự authenticated user)

---

### 🌐 **Initialize Guest Game G2**

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g2/init'
```

**Response Success:**
```json
{
    "status": "success",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
    "play_turns": 1
}
```

### 🎁 **Claim Guest Boxes G2**

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
  "box_ids": [1, 6, 9]
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g2/claim' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
    "box_ids": [1, 6, 9]
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Bạn đã mở hộp quà số 1, 6 và nhận được voucher! Đăng nhập để nhận phần thưởng.",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
    "play_turns": 0,
    "boxes": [
        {
            "id": 1,
            "reward": {
                "reward_id": 39,
                "name": "Voucher 100K",
                "image": "vc_100k"
            }
        },
        {
            "id": 2,
            "reward": {
                "reward_id": 42,
                "name": "Tặng miễn phí 1 chai nước rửa bát",
                "image": "nuoc_rua_bat"
            }
        },
        // ... 7 boxes khác
    ],
    "prizes_won": [
        {
            "id": 39,
            "name": "Voucher 100K",
            "type": "voucher"
        }
    ]
}
```

---

# 🎯 Tokyo Life Game G2 - API Usage Guide

Game G2 là phiên bản nâng cao với cơ chế mở hộp quà. Người chơi có thể chọn nhiều hộp cùng lúc để mở và nhận voucher.

### 1. 🚀 **Initialize Game G2**

Khởi tạo game G2, preload user data và game UI. User sẽ nhận thêm lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmZDFkZjBlZC02MTkxLTQxODEtYmJlNS04YmU5MDhlYzI0ZjgiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NDEwNTM0NywiaWF0IjoxNzUzNTAwNTQ3fQ.vE6Z0oYwzhzQxs98579zTAOzT4HoKb3iM0uBCdY-dvQ'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": "25442",
        "name": "Nguyễn Đạt"
    },
    "play_turns": 2,
    "is_share_turn": false,
    "first_play": false,
    "opened_boxes": [
        5
    ],
    "box_rewards": [
        {
            "box_id": 5,
            "reward": {
                "id": "40",
                "name": "Tặng miễn phí 1 áo Polo giá tới 249K với hóa đơn từ 799K",
                "image": "ao_polo",
                "type": "voucher",
                "has_voucher": true
            },
            "opened_at": "2025-08-01T10:35:59.578Z"
        }
    ]
}
```

### 2. 🎁 **Claim Boxes (Mở Hộp Quà)**

Chọn và mở nhiều hộp quà cùng lúc. Người chơi có thể chọn từ 1-9 hộp để mở.

**Request Body:**
```json
{
  "box_ids": [5],
  "context":"7"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmZDFkZjBlZC02MTkxLTQxODEtYmJlNS04YmU5MDhlYzI0ZjgiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NDEwNTM0NywiaWF0IjoxNzUzNTAwNTQ3fQ.vE6Z0oYwzhzQxs98579zTAOzT4HoKb3iM0uBCdY-dvQ' \
--header 'Content-Type: application/json' \
--data '{
  "box_ids": [5],
  "context":"7"
}'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn đã mở hộp quà số 5 và nhận được voucher!",
    "boxes": [
        {
            "id": 1,
            "reward": {
                "reward_id": "47",
                "name": "Code giảm 30% thời trang nguyên giá bất kỳ",
                "image": "thoi_trang_30"
            }
        },
        {
            "id": 2,
            "reward": {
                "reward_id": "69",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 3,
            "reward": {
                "reward_id": "46",
                "name": "Tặng miễn phí 1 nước lau sàn với hóa đơn bất kỳ từ 399K",
                "image": "nuoc_lau_san"
            }
        },
        {
            "id": 4,
            "reward": {
                "reward_id": "41",
                "name": "Giảm 30% kính gấp gọn mới",
                "image": "kinh_30"
            }
        },
        {
            "id": 5,
            "reward": {
                "reward_id": "40",
                "name": "Tặng miễn phí 1 áo Polo giá tới 249K với hóa đơn từ 799K",
                "image": "ao_polo"
            }
        },
        {
            "id": 6,
            "reward": {
                "reward_id": "36",
                "name": "Mã quay thưởng ô tô",
                "image": "ma_quay_o_to"
            }
        },
        {
            "id": 7,
            "reward": {
                "reward_id": "70",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 8,
            "reward": {
                "reward_id": "68",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 9,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        }
    ],
    "play_turns": 2
}
```

**Response (Hết lượt chơi):**
```json
{
    "status": "no_turns",
    "message": "Bạn đã hết lượt chơi hôm nay",
    "boxes": [
        {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
        {"id": 2, "reward": {"name": "Voucher 50K", "image": "/assets/v50k.png"}},
        {"id": 3, "reward": {"name": "Voucher 100K", "image": "/assets/v100k.png"}},
        {"id": 4, "reward": {"name": "Voucher 150K", "image": "/assets/v150k.png"}},
        {"id": 5, "reward": {"name": "Voucher 200K", "image": "/assets/v200k.png"}},
        {"id": 6, "reward": {"name": "Voucher 250K", "image": "/assets/v250k.png"}},
        {"id": 7, "reward": {"name": "Voucher 300K", "image": "/assets/v300k.png"}},
        {"id": 8, "reward": {"name": "Voucher 350K", "image": "/assets/v350k.png"}},
        {"id": 9, "reward": {"name": "Voucher 400K", "image": "/assets/v400k.png"}}
    ],
    "share_remaining": 1
}
```

### 3. 📤 **Share for Extra Turn G2**

Chia sẻ để nhận thêm 1 lượt chơi (giới hạn 1 lần mỗi ngày).

**Request Body:**
```json
{
  "platform": "facebook"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/share' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJjYTYxMjk3YS05ZDIwLTQyMzktOGFhMi1lYmI0NmQ3MGNmYTkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzMyNTQ4MiwiaWF0IjoxNzUyNzIwNjgyfQ.rtSSCoMLblX0S9kU2dqVnqwTpl2GZSbhCTGowVFhQoQ' \
--header 'Content-Type: application/json' \
--data '{
    "platform": "facebook"
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Bạn đã nhận thêm 1 lượt chơi!",
    "play_turns": 1
}
```

**Response (Đã share hôm nay):**
```json
{
    "status": "already_shared",
    "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```


*Last updated: 2025-07-17* 