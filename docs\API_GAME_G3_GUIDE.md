# 🎮 Tokyo Life Game G3 - API Usage Guide

Tài liệu hướng dẫn sử dụng API cho Game G3. T<PERSON>t cả endpoint yê<PERSON> cầu đăng nhập (Bearer Token).

## 🔐 Authentication
- Thêm header: `Authorization: Bearer <ACCESS_TOKEN>`

---

## 1. 🚀 Initialize Game G3

- Method: GET
- URL: `/api-v1/game3/init`

Ví dụ:
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/init' \
--header 'Authorization: Bearer <ACCESS_TOKEN>'
```

Response mẫu:
```json
{
    "status": "success",
    "user": {
        "id": "25439",
        "name": "KAM TEST"
    },
    "play_turns": 4,
    "letters": [
        "T"
    ],
    "locations": [
        {
            "id": 1,
            "name": "<PERSON>yê<PERSON>",
            "star": 0,
            "received": []
        },
        {
            "id": 2,
            "name": "<PERSON><PERSON><PERSON><PERSON>",
            "star": 0,
            "received": []
        },
        {
            "id": 3,
            "name": "<PERSON><PERSON>iang",
            "star": 0,
            "received": []
        },
        {
            "id": 4,
            "name": "<PERSON><PERSON>",
            "star": 0,
            "received": []
        }
    ]
}
```

---

## 2. 📜 Get Rules (Thể lệ tại 1 tỉnh thành)

- Method: POST
- URL: `/api-v1/game3/rules`
- Body:
```json
{
  "locationId": 1
}
```

```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/rules' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhNWQ3MmM3Ni1lN2FjLTQzM2QtYWRiMC02MGNiMGE5NTE1NzkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NTUwMTkyNSwiaWF0IjoxNzU0ODk3MTI1fQ.bnkQYS5i8Gr47DfgG3PKMZcI5b4fhcWAYq4LiYbhyB4' \
--header 'Content-Type: application/json' \
--data '{
    "locationId": "1"
}'
```

Response mẫu:
```json
{
    "status": "success",
    "rules": "<h3>Thể lệ chương trình tại Tuyên Quang</h3><p>Thu thập đủ 15 áo, 10 quần, 25 balo để nhận phần thưởng.</p>"
}
```

Lưu ý: Trường gửi là `locationId` (camelCase).

---

## 3. 🗺️ Danh sách tỉnh thành

- Method: GET
- URL: `/api-v1/game3/locations`

```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/locations' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhNWQ3MmM3Ni1lN2FjLTQzM2QtYWRiMC02MGNiMGE5NTE1NzkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NTUwMTkyNSwiaWF0IjoxNzU0ODk3MTI1fQ.bnkQYS5i8Gr47DfgG3PKMZcI5b4fhcWAYq4LiYbhyB4'
```

Response mẫu:
```json
{
    "status": "success",
    "locations": [
        {
            "id": 1,
            "name": "Tuyên Quang"
        },
        {
            "id": 2,
            "name": "Điện Biên"
        },
        {
            "id": 3,
            "name": "Hà Giang"
        },
        {
            "id": 4,
            "name": "Sapa"
        }
    ]
}
```

---

## 4. 🔎 Chi tiết yêu cầu vật phẩm theo tỉnh

- Method: GET
- URL: `/api-v1/game3/location-detail/:id`

```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/location-detail/1' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhNWQ3MmM3Ni1lN2FjLTQzM2QtYWRiMC02MGNiMGE5NTE1NzkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NTUwMTkyNSwiaWF0IjoxNzU0ODk3MTI1fQ.bnkQYS5i8Gr47DfgG3PKMZcI5b4fhcWAYq4LiYbhyB4'
```
Response mẫu:
```json
{
    "status": "success",
    "location_id": 1,
    "location_name": "Tuyên Quang",
    "required_items": [
        {
            "type": "ao",
            "name": "Áo",
            "required": 15
        },
        {
            "type": "balo",
            "name": "Balo",
            "required": 25
        },
        {
            "type": "letter",
            "name": "T",
            "required": 1
        },
        {
            "type": "letter",
            "name": "O",
            "required": 1
        },
        {
            "type": "letter",
            "name": "K",
            "required": 1
        },
        {
            "type": "quan",
            "name": "Quần",
            "required": 10
        }
    ]
}
```

---

## 5. ▶️ Bắt đầu chơi tại 1 tỉnh

- Method: GET
- URL: `/api-v1/game3/play/:location_id`

Ví dụ:
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/play/1' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhNWQ3MmM3Ni1lN2FjLTQzM2QtYWRiMC02MGNiMGE5NTE1NzkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NTUwMTkyNSwiaWF0IjoxNzU0ODk3MTI1fQ.bnkQYS5i8Gr47DfgG3PKMZcI5b4fhcWAYq4LiYbhyB4'
```

Response mẫu:
```json
{
    "status": "success",
    "play_turns": 4,
    "spawn_items": [
        {
            "type": "ao",
            "image": "item_ao"
        },
        {
            "type": "quan",
            "image": "item_quan"
        },
        {
            "type": "balo",
            "image": "item_balo"
        }
    ],
    "spawn_letters": [
        "T",
        "O",
        "K"
    ],
    "code": "g3_1754899586097_iymauj38u",
    "location": {
        "id": 1,
        "name": "Tuyên Quang",
        "rules": "<h3>Thể lệ chương trình tại Tuyên Quang</h3><p>Thu thập đủ 15 áo, 10 quần, 25 balo để nhận phần thưởng.</p>"
    }
}
```

---

## 6. 🏁 Kết thúc lượt chơi

- Method: POST
- URL: `/api-v1/game3/end`
- Body:
```json
{
  "code": "g3_1723456789_abcd12345",
  "location_id": 1,
  "duration": 120,
  "distance": 300,
  "collected_items": { "ao": 2, "quan": 3, "balo": 1 },
  "collected_letters": ["T"],
  "context": "7"
}
```

```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game3/end' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhNWQ3MmM3Ni1lN2FjLTQzM2QtYWRiMC02MGNiMGE5NTE1NzkiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NTUwMTkyNSwiaWF0IjoxNzU0ODk3MTI1fQ.bnkQYS5i8Gr47DfgG3PKMZcI5b4fhcWAYq4LiYbhyB4' \
--header 'Content-Type: application/json' \
--data '{
    "locationId": "1",
    "code": "g3_1754899586097_iymauj38u",
    "duration": 1000,
    "distance": 1000,
    "collected_items": {
        "ao": 2,
        "quan": 3,
        "balo": 1
    },
    "collected_letters": ["T"]
}'
```

Ghi chú: Có thể truyền `context` ở query (`/end?context=7`)

Response mẫu:
```json
{
    "status": "success",
    "play_turns": 3,
    "time_left": 1000,
    "distance_covered": 1000,
    "collected_items": {
        "ao": 4,
        "balo": 2,
        "quan": 6
    },
    "collected_letters": [
        "T",
        "T"
    ],
    "stars": 0,
    "location_completed": false,
    "tokyo_life_completed": false,
    "rewards": {
        "location_reward": null,
        "tokyo_life_reward": null
    }
}
```

---

## 🚨 Error Handling

- Thiếu/invalid tham số:
```json
{ "status": "error", "message": "Invalid location ID" }
```

- Chưa đăng nhập / token không hợp lệ:
```json
{ "status": "error", "message": "Unauthorized" }
```

- Hết lượt chơi:
```json
{ "status": "error", "message": "Bạn đã hết lượt chơi" }
```

- Phiên chơi không hợp lệ hoặc đã hết hạn:
```json
{ "status": "error", "message": "Phiên chơi không hợp lệ hoặc đã hết hạn" }
```

---
Last updated: 2025-08-11








