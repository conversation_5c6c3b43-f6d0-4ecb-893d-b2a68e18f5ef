### 1. 🚀 **Initialize Game G2**

Khởi tạo game G2, preload user data và game UI. User sẽ nhận thêm lượt chơi mỗi ngày mới.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/init' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmZDFkZjBlZC02MTkxLTQxODEtYmJlNS04YmU5MDhlYzI0ZjgiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NDEwNTM0NywiaWF0IjoxNzUzNTAwNTQ3fQ.vE6Z0oYwzhzQxs98579zTAOzT4HoKb3iM0uBCdY-dvQ'
```

**Response Success:**
```json
{
    "status": "success",
    "user": {
        "id": "25442",
        "name": "<PERSON><PERSON><PERSON><PERSON>"
    },
    "play_turns": 2,
    "is_share_turn": false,
    "first_play": false,
    "opened_boxes": [
        5
    ],
    "box_rewards": [
        {
            "box_id": 5,
            "reward": {
                "id": "40",
                "name": "Tặng miễn phí 1 áo Polo giá tới 249K với hóa đơn từ 799K",
                "image": "ao_polo",
                "type": "voucher",
                "has_voucher": true
            },
            "opened_at": "2025-08-01T10:35:59.578Z"
        }
    ]
}
```

### 2. 🎁 **Claim Boxes (Mở Hộp Quà)**

Chọn và mở nhiều hộp quà cùng lúc. Người chơi có thể chọn từ 1-9 hộp để mở.

**Request Body:**
```json
{
  "box_ids": [5],
  "context":"7"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/g2/claim' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJmZDFkZjBlZC02MTkxLTQxODEtYmJlNS04YmU5MDhlYzI0ZjgiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDQyIiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1NDEwNTM0NywiaWF0IjoxNzUzNTAwNTQ3fQ.vE6Z0oYwzhzQxs98579zTAOzT4HoKb3iM0uBCdY-dvQ' \
--header 'Content-Type: application/json' \
--data '{
  "box_ids": [5],
  "context":"7"
}'
```

**Response Success (Còn lượt chơi):**
```json
{
    "status": "success",
    "message": "Bạn đã mở hộp quà số 5 và nhận được voucher!",
    "boxes": [
        {
            "id": 1,
            "reward": {
                "reward_id": "47",
                "name": "Code giảm 30% thời trang nguyên giá bất kỳ",
                "image": "thoi_trang_30"
            }
        },
        {
            "id": 2,
            "reward": {
                "reward_id": "69",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 3,
            "reward": {
                "reward_id": "46",
                "name": "Tặng miễn phí 1 nước lau sàn với hóa đơn bất kỳ từ 399K",
                "image": "nuoc_lau_san"
            }
        },
        {
            "id": 4,
            "reward": {
                "reward_id": "41",
                "name": "Giảm 30% kính gấp gọn mới",
                "image": "kinh_30"
            }
        },
        {
            "id": 5,
            "reward": {
                "reward_id": "40",
                "name": "Tặng miễn phí 1 áo Polo giá tới 249K với hóa đơn từ 799K",
                "image": "ao_polo"
            }
        },
        {
            "id": 6,
            "reward": {
                "reward_id": "36",
                "name": "Mã quay thưởng ô tô",
                "image": "ma_quay_o_to"
            }
        },
        {
            "id": 7,
            "reward": {
                "reward_id": "70",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 8,
            "reward": {
                "reward_id": "68",
                "name": "Chúc bạn may mắn lần sau",
                "image": "chuc_mm"
            }
        },
        {
            "id": 9,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        }
    ],
    "play_turns": 2
}
```

**Response (Hết lượt chơi):**
```json
{
    "status": "no_turns",
    "message": "Bạn đã hết lượt chơi hôm nay",
    "boxes": [
        {"id": 1, "reward": {"name": "Voucher 20K", "image": "/assets/v20k.png"}},
        {"id": 2, "reward": {"name": "Voucher 50K", "image": "/assets/v50k.png"}},
        {"id": 3, "reward": {"name": "Voucher 100K", "image": "/assets/v100k.png"}},
        {"id": 4, "reward": {"name": "Voucher 150K", "image": "/assets/v150k.png"}},
        {"id": 5, "reward": {"name": "Voucher 200K", "image": "/assets/v200k.png"}},
        {"id": 6, "reward": {"name": "Voucher 250K", "image": "/assets/v250k.png"}},
        {"id": 7, "reward": {"name": "Voucher 300K", "image": "/assets/v300k.png"}},
        {"id": 8, "reward": {"name": "Voucher 350K", "image": "/assets/v350k.png"}},
        {"id": 9, "reward": {"name": "Voucher 400K", "image": "/assets/v400k.png"}}
    ],
    "share_remaining": 1
}
```