import { Column, <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm'
import { LuckyPrize } from './LuckyPrize'

@Entity('game_user_rewards')
export class GameUserRewards {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('bigint', { name: 'user_id', unsigned: true })
  userId: number

  @Column('int', { name: 'game_id', nullable: true })
  gameId: number | null

  @Column('varchar', { name: 'reward_type', length: 50, nullable: true }) // 'location_items' hoặc 'complete_letters'
  rewardType: string | null

  @Column('int', { name: 'location_id', default: 0 })
  locationId: number

  @Column('int', { name: 'star_count', nullable: true }) // số sao đạt được khi nhận reward (1-3)
  starCount: number | null

  @Column('bigint', { name: 'prize_id', unsigned: true, nullable: true })
  prizeId: number | null

  @Column('timestamp', { name: 'claimed_at', nullable: true })
  claimedAt: Date | null

  @Column('int', { name: 'biz_coupon_id', nullable: true })
  bizCouponId: number | null

  @Column('varchar', { name: 'voucher_code', length: 128, nullable: true })
  voucherCode: string | null

  @Column('varchar', { name: 'voucher_link', length: 255, nullable: true })
  voucherLink: string | null

  @Column('varchar', { name: 'voucher_name', length: 255, nullable: true })
  voucherName: string | null

  @Column('varchar', { name: 'voucher_expried', length: 255, nullable: true })
  voucherExpried: string | null

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null

  @Column('timestamp', { name: 'updated_at', nullable: true })
  updatedAt: Date | null

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null

  @ManyToOne(() => LuckyPrize)
  @JoinColumn({ name: 'prize_id' })
  prize: LuckyPrize
}
