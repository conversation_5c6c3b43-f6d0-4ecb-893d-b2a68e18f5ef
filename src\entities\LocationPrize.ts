import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { Location } from './Location'
import { LuckyPrize } from './LuckyPrize'

@Entity('location_prizes', { schema: 'events' })
export class LocationPrize extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('int', { name: 'prize_id' })
  prizeId: number

  @Column('int', { name: 'context', nullable: true })
  context: number | null

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location

  @ManyToOne(() => LuckyPrize)
  @JoinColumn({ name: 'prize_id' })
  prize: LuckyPrize
}



