import express from 'express'
import { commonService, gameG1Service } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { AppDataSource } from '@/config/config'
import { LogActivity } from '@/entities/LogActivity'
import { DrawGiftRequest } from '@/types/requests/game'
import { CommonService } from '@/services/common.service'

// Helper function để lấy thời gian GMT+7
const getNowGMT7 = () => {
  return new Date(new Date().getTime() + 7 * 60 * 60 * 1000)
}

const subtract7Hours = (times: Date) => {
  return new Date(new Date(times).getTime() - 7 * 60 * 60 * 1000)
}

// Helper function để log activity
const logActivityRepo = () => AppDataSource.getRepository(LogActivity)

async function logActivity(
  userId: number, 
  name: string, 
  url: string, 
  method: string, 
  requestData: any, 
  result: any, 
  ip?: string, 
  agent?: string
) {
  try {
    const log = new LogActivity()
    log.user_id = userId
    log.name = name
    log.message = `GameG1 API ${name} - Status: ${result?.status || 'unknown'}`
    log.full_message = JSON.stringify(result)
    log.url = url
    log.method = method
    log.ip = ip
    log.agent = agent
    log.form_data = JSON.stringify(requestData)
    log.created_at = getNowGMT7()
    log.updated_at = getNowGMT7()
    
    await logActivityRepo().save(log)
  } catch (error) {
    console.error('Error logging activity:', error)
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
const initGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  const startTime = Date.now()
  let result: any = null
  
  try {
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, params: req.params, query: req.query }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'initGame',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.initGame(req, slug)
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'initGame',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug: req.params.slug, params: req.params, query: req.query }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'initGame - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
const playGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, params: req.params, query: req.query }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'playGame',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.playGame(req, slug)
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'playGame',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug: req.params.slug, params: req.params, query: req.query }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'playGame - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
const claimVoucherHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null

  try {
    const { slug } = req.params
    const { box_ids: boxIds, context } = req.body
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, boxIds, context, params: req.params, body: req.body }

    if (!slug) {
      result = {
        status: 'error',
        message: 'Missing game slug'
      }

      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'claimVoucher',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }

      return res.status(200).json(result)
    }

    result = await gameG1Service.claimVoucher(
      req,
      slug,
      boxIds,
      context
    )
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'claimVoucher',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    console.log('claimVoucherHandler error', error)
    const userId = req.authorizedUser?.user?.id
    const requestData = {
      slug: req.params.slug,
      boxIds: req.body.box_ids,
      context: req.body.context,
      params: req.params,
      body: req.body
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'claimVoucher - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
const shareForExtraTurnHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { slug } = req.params
    const { platform } = req.body
    const userId = req.authorizedUser?.user?.id
    const requestData = { slug, platform, params: req.params, body: req.body }

    if (!slug) {
      result = { 
        status: 'error', 
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'shareForExtraTurn',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    if (!platform) {
      result = { 
        status: 'error', 
        message: 'Missing platform' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'shareForExtraTurn',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.shareForExtraTurn(
      req, 
      slug, 
      platform
    )
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'shareForExtraTurn',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { 
      slug: req.params.slug, 
      platform: req.body.platform, 
      params: req.params, 
      body: req.body 
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'shareForExtraTurn - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * 5. POST /api/game/history
 * Lấy SpinHistory của từng game
 */
const getGameHistoryHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser
    const { slug } = req.params

    // Convert slug to gameId using the helper function
    const gameId = CommonService.convertGameIdSlugToNumber(slug)

    if (!gameId) {
      return res.status(200).json({
        status: 200,
        error_code: 1,
        message: 'Missing gameId or campaignId'
      })
    }

    const history = await gameG1Service.getSpinHistoryByUserGameCampaign(
      user.id,
      gameId
    )

    const result = history.map(item => ({
      id: item.id,
      spin_time: subtract7Hours(item.spinTime),
      prize_id: item.prizeId,
      prize_name: item.prize?.name || null,
      prize_image: item.prize?.image || null,
      voucher_code: item.voucherCode,
      voucher_name: item.voucherName,
      voucher_link: item.voucherLink,
      voucher_expried: item.voucherExpried,
      status: item.status,
      claimed_at: item.claimedAt,
      description: item.description,
      address: item.address
    }))

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

/**
 * 6. POST /api/game/voucher-storage
 * Lấy SpinHistory theo user 
 */
const getVoucherStorageHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { user } = req.authorizedUser

    const history = await gameG1Service.getSpinHistoryWithVoucherByUserCampaign(
      user.id
    )

    const result = history.map(item => ({
      id: item.id,
      spin_time: subtract7Hours(item.spinTime),
      prize_id: item.prizeId,
      prize_name: item.prize?.name || null,
      prize_image: item.prize?.image || null,
      voucher_code: item.voucherCode,
      voucher_name: item.voucherName,
      voucher_link: item.voucherLink,
      voucher_expried: item.voucherExpried,
      status: item.status,
      claimed_at: item.claimedAt,
      game_id: item.gameId,
      campaign_id: item.campaignId
    }))

    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result
    })
  } catch (e) {
    return res.status(200).json({ status: 200, error_code: 1, message: e.message })
  }
}

/**
 * 7. POST /api/game/invite-reward
 * Tặng quà cho người mời bạn bè (random từ prize voucher/spin/lose theo tỉ lệ trúng)
 */
const giveInviteRewardHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { user_id: inviterUserId } = req.body
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id
    const requestData = { inviterUserId, slug, body: req.body, params: req.params }

    // Kiểm tra các tham số bắt buộc
    if (!inviterUserId) {
      result = { 
        status: 200,
        error_code: 1,
        message: 'Missing user_id (người mời)' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'giveInviteReward',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    if (!slug) {
      result = { 
        status: 200,
        error_code: 1,
        message: 'Missing game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'giveInviteReward',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    // Convert slug to gameId
    const gameId = CommonService.convertGameIdSlugToNumber(slug)
    if (!gameId) {
      result = { 
        status: 200,
        error_code: 1,
        message: 'Invalid game slug' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'giveInviteReward',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }



    result = await gameG1Service.giveInviteReward(
      req,
      parseInt(inviterUserId),
      gameId
    )
    
    // Chuyển đổi format response
    const response = {
      status: 200,
      error_code: 0,
      message: result.message,
      data: result.data
    }
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'giveInviteReward',
        req.originalUrl,
        req.method,
        requestData,
        response,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(response)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { 
      inviterUserId: req.body.user_id,
      slug: req.params.slug,
      body: req.body,
      params: req.params
    }
    
    result = { 
      status: 200,
      error_code: 1,
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'giveInviteReward - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

const drawGiftHandler = async (req: DrawGiftRequest, res: express.Response) => {
  try {
    const { campaignId, prizeId } = req.body
    const { user } = req.authorizedUser
    
    if (!campaignId || !prizeId) {
      return res.status(200).json({ 
        status: 200, 
        error_code: 1, 
        message: 'Missing campaignId or prizeId' 
      })
    }

    const result = await gameG1Service.drawGift(campaignId, prizeId, user.id)
    
    return res.status(200).json({
      status: 200,
      error_code: 0,
      data: result,
      message: result.message
    })
  } catch (e) {
    return res.status(200).json({ 
      status: 200, 
      error_code: 1, 
      message: e.message 
    })
  }
}

/**
 * POST /api/game/:slug/claim-prize
 * Claim một prize cụ thể bằng prizeId (không random)
 */
const claimPrizeHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null

  try {
    const { slug } = req.params
    const { prize_id: prizeId, user_id: userId } = req.body
    const requestData = { slug, prizeId, params: req.params, body: req.body }

    if (!slug) {
      result = {
        status: 'error',
        message: 'Missing game slug'
      }

      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'claimPrize',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }

      return res.status(200).json(result)
    }

    if (!prizeId) {
      result = {
        status: 'error',
        message: 'Missing prize_id'
      }

      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'claimPrize',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }

      return res.status(200).json(result)
    }

    result = await gameG1Service.claimPrize(
      req,
      slug,
      parseInt(prizeId),
      parseInt(userId)
    )
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'claimPrize',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    console.log('claimPrizeHandler error', error)
    const userId = req.authorizedUser?.user?.id
    const requestData = {
      slug: req.params.slug,
      prizeId: req.body.prize_id,
      params: req.params,
      body: req.body
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'claimPrize - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

/**
 * Merge guest data khi user đăng nhập
 */
const mergeGuestDataHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  let result: any = null
  
  try {
    const { guest_session_id: guestSessionId } = req.body
    const userId = req.authorizedUser?.user?.id
    const requestData = { guestSessionId, body: req.body }

    if (!guestSessionId) {
      result = { 
        status: 'error', 
        message: 'Missing guest_session_id' 
      }
      
      // Log activity
      if (userId) {
        await logActivity(
          userId,
          'mergeGuestData',
          req.originalUrl,
          req.method,
          requestData,
          result,
          req.ip,
          req.get('User-Agent')
        )
      }
      
      return res.status(200).json(result)
    }

    if (!userId) {
      result = { 
        status: 'error', 
        message: 'User not authenticated' 
      }
      
      return res.status(200).json(result)
    }

    result = await gameG1Service.mergeGuestDataToUser(userId, guestSessionId)
    
    // Log successful activity
    if (userId) {
      await logActivity(
        userId,
        'mergeGuestData',
        req.originalUrl,
        req.method,
        requestData,
        result,
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  } catch (error) {
    const userId = req.authorizedUser?.user?.id
    const requestData = { 
      guestSessionId: req.body.guest_session_id,
      body: req.body
    }
    
    result = { 
      status: 'error', 
      message: error.message 
    }
    
    // Log error activity
    if (userId) {
      await logActivity(
        userId,
        'mergeGuestData - ERROR',
        req.originalUrl,
        req.method,
        requestData,
        { ...result, error: error.message, stack: error.stack },
        req.ip,
        req.get('User-Agent')
      )
    }
    
    return res.status(200).json(result)
  }
}

const resetGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { slug } = req.params
    const userId = req.authorizedUser?.user?.id

    if (!slug) {
      return res.status(400).json({ message: 'Missing game slug' })
    }

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' })
    }

    const result = await gameG1Service.resetGame(userId, slug)
    return res.status(200).json(result)
  } catch (error) {
    return res.status(500).json({ message: error.message })
  }
}

export { 
  initGameHandler, 
  playGameHandler, 
  claimVoucherHandler, 
  claimPrizeHandler,
  shareForExtraTurnHandler,
  getGameHistoryHandler,
  getVoucherStorageHandler,
  giveInviteRewardHandler,
  drawGiftHandler,
  mergeGuestDataHandler,
  resetGameHandler
}
