import { AppDataSource } from '@/config/config'
import {
  TblGames,
  Location,
  LocationRequirement,
  UserSpin,
  Game3UserItems,
  GameUserRewards,
  Game3PlaySession,
  SpinHistory,
  LocationPrize,
  ItemsGame
} from '@/entities'
import { Game3SessionStatus } from '@/entities/Game3PlaySession'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { NotFoundError, InputError } from '@/utils/ApiError'
import moment from 'moment'
import { getPrizesWithRemainingQuantity, randomPrizeWithQuantityCheck } from './gameG1.service'
import * as luckyWheelService from './luckyWheel.service'
import { CommonService } from './common.service'

// Helper functions
const gameRepo = () => AppDataSource.getRepository(TblGames)
const locationRepo = () => AppDataSource.getRepository(Location)
const locationRequirementRepo = () => AppDataSource.getRepository(LocationRequirement)
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const userItemsRepo = () => AppDataSource.getRepository(Game3UserItems)
const userRewardsRepo = () => AppDataSource.getRepository(GameUserRewards)
const playSessionRepo = () => AppDataSource.getRepository(Game3PlaySession)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const locationPrizeRepo = () => AppDataSource.getRepository(LocationPrize)
const itemsGameRepo = () => AppDataSource.getRepository(ItemsGame)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return moment.utc().add(7, 'hours').toDate()
}

// Helper function to process coupon from Bizfly (similar to gameG1)
interface CouponInfo { couponId: number; couponCode: string; couponName: string; qrCodeLink: string; end_date: string }
interface CouponProcessResult { success: boolean; couponData?: CouponInfo; message: string }

const processCouponFromBizfly = async (
  user: { id: number; tokyoId: string; bizId: string },
  prize: { id: number; name: string; bizStorageId?: number; remainingQuantity?: number },
  game: { name: string },
  campaignId: number,
  gameId: number
) : Promise<CouponProcessResult> => {
  if (!prize.bizStorageId) {
    return {
      success: true,
      message: prize.name
    }
  }

  try {
    if (prize.remainingQuantity > 0) {
      const couponResponse = await luckyWheelService.getCouponFromBizfly(
        user.id,
        prize.bizStorageId,
        user.tokyoId,
        user.bizId,
        game.name,
        campaignId,
        gameId,
        gameId == 27 ? 4 : 5
      )

      if (couponResponse && couponResponse.data) {
        const couponData = {
          couponId: couponResponse.data.coupon_id,
          couponCode: couponResponse.data.code,
          couponName: couponResponse.data.name,
          qrCodeLink: couponResponse.data.link_scan_qr_code,
          end_date: couponResponse.data.end_date
        }

        return {
          success: true,
          couponData,
          message: `Bạn nhận được ${prize.name}`
        }
      }
    }

    return {
      success: false,
      message: prize.name
    }
  } catch (error) {
    return {
      success: false,
      message: prize.name
    }
  }
}



// Helper function to generate unique session code
const generateSessionCode = (): string => {
  return `g3_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Helper function to check if location requirements are completed
const checkLocationCompletion = (userItems: Game3UserItems[], requirements: LocationRequirement[]) => {
  const userItemsMap = new Map<number, number>()

  userItems.forEach(item => {
    userItemsMap.set(item.itemId, item.quantity || 0)
  })

  for (const req of requirements) {
    const userQuantity = userItemsMap.get(req.itemId) || 0
    if (userQuantity < req.requiredQuantity) {
      return false
    }
  }

  return true
}

// Helper function to calculate stars based on item quantity vs requirements
const calculateStars = (userItems: Game3UserItems[], requirements: LocationRequirement[]) => {
  const totalRequirements = requirements.length
  let totalCollectedQuantity = 0
  let totalRequiredQuantity = 0

  if (totalRequirements === 0) return 0

  const userItemsMap = new Map<number, number>()
  userItems.forEach(item => {
    userItemsMap.set(item.itemId, item.quantity || 0)
  })

  requirements.forEach(req => {
    const userQuantity = Math.min(userItemsMap.get(req.itemId) || 0 , req.requiredQuantity) 
    totalCollectedQuantity += userQuantity
    totalRequiredQuantity += req.requiredQuantity
  })

  const completionRate = totalRequiredQuantity > 0 ? totalCollectedQuantity / totalRequiredQuantity : 0;

  if (completionRate >= 1.0) return 3  // Hoàn thành tất cả requirements
  if (completionRate >= 0.8) return 2 // Hoàn thành >= 2/3 requirements  
  if (completionRate >= 0.5) return 1 // Hoàn thành >= 1/3 requirements
  return 0
}

// Helper function to check if user has collected all TOKYOLIFE letters
const checkTokyoLifeCompletion = (allUserItems: Game3UserItems[]) => {
  const requiredLetters = ['T', 'O', 'K', 'Y', 'O', 'L', 'I', 'F', 'E']
  const letterCounts = new Map<string, number>()

  // Đếm số lượng từng chữ cái (cần itemsGame relation để check type)
  allUserItems.forEach(item => {
    if (item.itemsGame && item.itemsGame.type === 'letter') {
      const letter = item.itemsGame.name // Sử dụng name từ ItemsGame
      letterCounts.set(letter, (letterCounts.get(letter) || 0) + item.quantity)
    }
  })

  // Kiểm tra có đủ từng chữ cái không (cần ít nhất 1 của mỗi loại)
  for (const letter of requiredLetters) {
    if ((letterCounts.get(letter) || 0) < 1) {
      return false
    }
  }

  return true
}

const calculateSpawnQuantityExponential = (winrate: number, max: number, min = 1) => {
  const randomChance = Math.random() * 100;
  if (randomChance > winrate) {
    return 0;
  }
  
  // Sử dụng hàm exponential để tạo correlation mạnh hơn
  const winrateRatio = winrate / 100;
  
  // Tạo exponential bias: Math.pow để tăng cường effect
  const bias = Math.pow(winrateRatio, 0.5); // Có thể điều chỉnh exponent
  
  // Random với bias
  const randomValue = Math.random();
  const biasedRandom = randomValue * (1 - bias) + bias;
  
  // Scale về range [min, max]
  const quantity = Math.floor(biasedRandom * (max - min + 1)) + min;
  
  return Math.min(max, Math.max(min, quantity));
}

// Helper: lấy prizes theo location + context (stars) và tính remainingQuantity
const getLocationPrizesWithRemainingQuantity = async (
  campaignId: number,
  gameId: number,
  locationId: number,
  context: number
) => {
  // Lấy các mapping prize theo location/context
  const mappings = await locationPrizeRepo()
    .createQueryBuilder('lp')
    .leftJoinAndSelect('lp.prize', 'prize')
    .where('lp.locationId = :locationId', { locationId })
    .andWhere('lp.context = :context', { context })
    .andWhere('prize.campaignId = :campaignId', { campaignId })
    .andWhere('prize.gameId = :gameId', { gameId })
    .andWhere('prize.active = 1')
    .orderBy('prize.displayOrder', 'ASC')
    .getMany()

  const prizes = mappings
    .map(m => m.prize)
    .filter(Boolean)

  if (prizes.length === 0) return []

  // Batch đếm số lần đã trúng cho các prize voucher/spin có bizStorageId
  const voucherPrizeIds = prizes
    .filter(p => ['voucher', 'spin'].includes(p.type) && p.bizStorageId)
    .map(p => p.id)

  const wonCountMap = new Map<number, number>()
  if (voucherPrizeIds.length > 0) {
    const wonCounts = await spinHistoryRepo()
      .createQueryBuilder('spin_history')
      .select('spin_history.prizeId', 'prizeId')
      .addSelect('COUNT(*)', 'count')
      .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucherPrizeIds })
      .groupBy('spin_history.prizeId')
      .getRawMany()
    wonCounts.forEach(item => {
      wonCountMap.set(parseInt(item.prizeId), parseInt(item.count))
    })
  }

  const prizesWithRemaining = prizes.map(p => {
    let remainingQuantity = p.quantity
    if (['voucher', 'spin'].includes(p.type) && p.bizStorageId) {
      const won = wonCountMap.get(p.id) || 0
      remainingQuantity = p.quantity - won
    }
    return {
      ...p,
      remainingQuantity
    }
  })

  return prizesWithRemaining
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
export const initGame = async (req: AuthorizedUserRequest) => {
  const { user } = req.authorizedUser
  const gameId = 29;

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy hoặc tạo user spin (progress)
  let userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    userSpin = new UserSpin()
    userSpin.userId = user.id
    userSpin.campaignId = campaignId
    userSpin.gameId = gameId
    userSpin.spinCounts = 3 // Default 3 turns for game3
    userSpin.inviteCount = 0
    userSpin.createdAt = getNowGMT7()
    userSpin.updatedAt = getNowGMT7()
    userSpin.lastRequest = getNowGMT7()
    await userSpinRepo().save(userSpin)
  }else{
    const now = getNowGMT7()

    // Kiểm tra nếu lastRequest là ngày hôm trước thì reset dữ liệu ngày mới
    if (!userSpin.lastRequest || !CommonService.isSameDay(userSpin.lastRequest, now)) {
      // Reset totalSpinToday và openedBoxes khi sang ngày mới
      userSpin.totalSpinToday = 0
      userSpin.openedBoxes = null
      userSpin.lastRequest = now

      if(userSpin.spinCounts == 0){
        // Kiểm tra nếu lastRequest là ngày hôm trước thì cộng thêm lượt chơi cho ngày mới
        userSpin.spinCounts += CommonService.getDefaultSpinCountsByGameId(gameId)
      }else{
        const defaultSpinCounts = CommonService.getDefaultSpinCountsByGameId(gameId)
        if(userSpin.spinCounts < defaultSpinCounts){
          userSpin.spinCounts = defaultSpinCounts
        }
      }

      userSpin.updatedAt = now
      await userSpinRepo().save(userSpin)
    }
  }

  // Lấy danh sách locations với rewards đã nhận
  const locations = await locationRepo().find({
    where: { active: 1, type: 'game3' },
    order: { sortOrder: 'ASC' }
  })

  const locationsWithRewards = await Promise.all(
    locations.map(async (location) => {
      // Lấy tất cả rewards đã nhận cho location này
      const locationRewards = await userRewardsRepo().find({
        where: {
          userId: user.id,
          gameId,
          rewardType: 'location_items',
          locationId: location.id
        },
        relations: ['prize'],
        order: { claimedAt: 'ASC' }
      })

      // Tính toán số star dựa trên việc user đã thu thập items theo yêu cầu
      let star = 0
      
      // Lấy tất cả requirements của location này
      const locationRequirements = await locationRequirementRepo().find({
        where: {
          locationId: location.id,
          gameId
        },
        relations: ['item']
      })

      if (locationRequirements.length > 0) {
        // Lấy userItems cho location này
        const userItems = await userItemsRepo().find({
          where: {
            userId: user.id,
            gameId,
            locationId: location.id
          }
        })

        // Sử dụng hàm calculateStars để tính star theo tỉ lệ
        star = calculateStars(userItems, locationRequirements)
      }

      return {
        id: location.id,
        name: location.name,
        star: star,
        received: locationRewards.map(reward => ({
          reward_id: reward.prizeId,
          name: reward.prize.name,
          claimed_at: reward.claimedAt,
          image: reward.prize.image
        }))
      }
    })
  )

  // Lấy tất cả letters đã thu thập từ userItems
  const allUserItems = await userItemsRepo().find({
    where: {
      userId: user.id,
      gameId
    },
    relations: ['itemsGame']
  })

  const collectedLetters: string[] = []
  allUserItems.forEach(item => {
    // Kiểm tra nếu là letter item (dựa vào itemsGame.type)
    if (item.itemsGame && item.itemsGame.type === 'letter') {
      const letter = item.itemsGame.name // Sử dụng name từ ItemsGame
      for (let i = 0; i < item.quantity; i++) {
        collectedLetters.push(letter)
      }
    }
  })

  return {
    status: 'success',
    user: {
      id: user.id,
      name: user.name
    },
    play_turns: userSpin.spinCounts + (userSpin.inviteCount || 0),
    letters: collectedLetters,
    locations: locationsWithRewards
  }
}

/**
 * 2. POST /api/game/rules
 * Hiển thị thể lệ chương trình
 */
export const getRules = async (locationId: number) => {
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  return {
    status: 'success',
    rules: location.rules || `Nội dung chi tiết dạng HTML thể lệ chương trình cho ${location.name}.`
  }
}

/**
 * 3. GET /api/game/locations
 * Danh sách các tỉnh thành có thể chọn
 */
export const getLocations = async () => {
  const locations = await locationRepo().find({
    where: { active: 1 },
    order: { sortOrder: 'ASC' }
  })

  return {
    status: 'success',
    locations: locations.map(location => ({
      id: location.id,
      name: location.name
    }))
  }
}

/**
 * 4. GET /api/game/location-detail/:id
 * Lấy yêu cầu combo vật phẩm của từng tỉnh thành
 */
export const getLocationDetail = async (locationId: number) => {
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  const requirements = await locationRequirementRepo().find({
    where: { locationId },
    relations: ['item']
  })

  return {
    status: 'success',
    location_id: location.id,
    location_name: location.name,
    required_items: requirements.map(req => ({
      type: req.item.type,
      name: req.item.name,
      required: req.requiredQuantity
    }))
  }
}

/**
 * 5. GET /api/game/play/:location_id
 * Bắt đầu game tại 1 tỉnh thành, trả về danh sách vật phẩm & chữ cái có thể xuất hiện
 */
export const playGame = async (req: AuthorizedUserRequest, locationId: number) => {
  const { user } = req.authorizedUser
  const gameId = 29

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Kiểm tra location có tồn tại
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  // Lấy user spin (progress)
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  const totalTurns = (userSpin?.spinCounts || 0) + (userSpin?.inviteCount || 0)
  if (!userSpin || totalTurns <= 0) {
    throw new InputError('Bạn đã hết lượt chơi', '', 1)
  }

  // Tạo session code
  const sessionCode = generateSessionCode()

  // Lấy danh sách requirements cho location này (kèm thông tin item)
  const requirements = await locationRequirementRepo().find({
    where: { locationId: locationId },
    relations: ['item']
  })

  // Phân loại items và letters từ requirements
  const spawnItems = requirements
    .filter(requirement => requirement.item.type !== 'letter')
    .map(requirement => {
      const quantity = calculateSpawnQuantityExponential(
        (requirement.winrate ?? 100), 
        (requirement.max ?? 1),
        (requirement.min ?? 1)
      );
      
      return {
        type: requirement.item.type,
        image: `item_${requirement.item.type}`,
        quantity: quantity,
        itemName: requirement.item.name
      };
    })
    .filter(item => item.quantity > 0)

  const spawnLetters = requirements
    .filter(requirement => requirement.item.type === 'letter')
    .map(requirement => {
      const quantity = calculateSpawnQuantityExponential(
        (requirement.winrate ?? 100), 
        (requirement.max ?? 1),
        (requirement.min ?? 1)
      );
      
      return {
        letter: requirement.item.name,
        quantity: quantity
      }
    })
    .filter(letter => letter.quantity > 0)

  // Tạo play session
  const playSession = new Game3PlaySession()
  playSession.code = sessionCode
  playSession.userId = user.id
  playSession.gameId = gameId
  playSession.locationId = locationId
  playSession.spawnItems = spawnItems
  playSession.spawnLetters = spawnLetters
  playSession.expiresAt = moment().add(10, 'minutes').toDate() // Session expires in 10 minutes
  playSession.createdAt = getNowGMT7()
  playSession.updatedAt = getNowGMT7()

  await playSessionRepo().save(playSession)

  const remainingTurns = userSpin.spinCounts + userSpin.inviteCount

  return {
    status: 'success',
    play_turns: remainingTurns,
    spawn_items: spawnItems,
    spawn_letters: spawnLetters,
    code: sessionCode,
    location: {
      id: location.id,
      name: location.name,
      rules: location.rules
    }
  }
}

/**
 * 6. POST /api/game/end
 * Kết thúc lượt chơi, xác nhận số vật phẩm và chữ cái thu thập được
 */
interface EndGameData {
  code: string
  location_id: number
  duration: number
  distance: number
  collected_items: Record<string, number>
  collected_letters: string[]
  context?: string
}

export const endGame = async (req: AuthorizedUserRequest, endGameData: EndGameData) => {
  const { user } = req.authorizedUser
  const gameId = 29;
  const { code, location_id: locationId, duration, distance, collected_items: collectedItems, collected_letters: collectedLetters } = endGameData

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Kiểm tra session có tồn tại và hợp lệ
  const playSession = await playSessionRepo().findOne({
    where: {
      code,
      userId: user.id,
      gameId,
      locationId: locationId,
      status: Game3SessionStatus.ACTIVE
    }
  })

  if (!playSession) {
    throw new InputError('Phiên chơi không hợp lệ hoặc đã hết hạn', '', 1)
  }

  // Kiểm tra session có hết hạn chưa
  if (new Date() > playSession.expiresAt) {
    throw new InputError('Phiên chơi đã hết hạn', '', 1)
  }

  // Lấy user spin (progress)
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Không tìm thấy tiến trình game của user', '', 1)
  }

  // Lấy danh sách requirements cho location này để validation
  const locationRequirements = await locationRequirementRepo().find({
    where: { locationId: locationId },
    relations: ['item']
  })

  // Tạo map để kiểm tra và mapping itemType -> itemId
  const itemTypeToIdMap = new Map<string, { itemId: number, bounds: { min: number, max: number } }>()
  const letterNameToIdMap = new Map<string, { itemId: number, bounds: { min: number, max: number } }>()

  locationRequirements.forEach(requirement => {
    if (requirement.item.type === 'letter' && requirement.item.name) {
      letterNameToIdMap.set(requirement.item.name, {
        itemId: requirement.item.id,
        bounds: { min: requirement.min || 0, max: requirement.max || 999 }
      })
    } else if (requirement.item.type) {
      itemTypeToIdMap.set(requirement.item.type, {
        itemId: requirement.item.id,
        bounds: { min: requirement.min || 0, max: requirement.max || 999 }
      })
    }
  })

  // Validation collected_items
  if (collectedItems) {
    for (const [itemType, quantity] of Object.entries(collectedItems)) {
      const itemInfo = itemTypeToIdMap.get(itemType)
      if (!itemInfo) {
        throw new InputError(`Item '${itemType}' không tồn tại trong location ${locationId}`, '', 1)
      }
      
      if (quantity < itemInfo.bounds.min || quantity > itemInfo.bounds.max) {
        throw new InputError(`Số lượng item '${itemType}' phải từ ${itemInfo.bounds.min} đến ${itemInfo.bounds.max}`, '', 1)
      }
    }
  }

  // Validation collected_letters
  if (collectedLetters && Array.isArray(collectedLetters)) {
    // Đếm số lượng từng chữ cái
    const letterCounts: Record<string, number> = {}
    for (const letter of collectedLetters) {
      letterCounts[letter] = (letterCounts[letter] || 0) + 1
    }
    
    // Kiểm tra từng chữ cái
    for (const [letter, count] of Object.entries(letterCounts)) {
      const letterInfo = letterNameToIdMap.get(letter)
      if (!letterInfo) {
        throw new InputError(`Letter '${letter}' không tồn tại trong location ${locationId}`, '', 1)
      }
      
      if (count < letterInfo.bounds.min || count > letterInfo.bounds.max) {
        throw new InputError(`Số lượng letter '${letter}' phải từ ${letterInfo.bounds.min} đến ${letterInfo.bounds.max}`, '', 1)
      }
    }
  }

  // Cập nhật items cho user
  if (collectedItems) {
    for (const [itemType, quantity] of Object.entries(collectedItems)) {
      if (typeof quantity === 'number' && quantity > 0) {
        const itemInfo = itemTypeToIdMap.get(itemType)
        if (!itemInfo) continue

        let userItem = await userItemsRepo().findOne({
          where: {
            userId: user.id,
            gameId,
            locationId: locationId,
            itemId: itemInfo.itemId
          }
        })

        if (!userItem) {
          userItem = new Game3UserItems()
          userItem.userId = user.id
          userItem.gameId = gameId
          userItem.locationId = locationId
          userItem.itemId = itemInfo.itemId
          userItem.quantity = quantity
          userItem.used = 0
          userItem.createdAt = getNowGMT7()
        } else {
          userItem.quantity += quantity
        }

        userItem.updatedAt = getNowGMT7()
        await userItemsRepo().save(userItem)
      }
    }
  }

  // Cập nhật letters cho user - lưu như items
  if (collectedLetters && Array.isArray(collectedLetters)) {
    for (const letter of collectedLetters) {
      const letterInfo = letterNameToIdMap.get(letter)
      if (!letterInfo) continue

      let userItem = await userItemsRepo().findOne({
        where: {
          userId: user.id,
          gameId,
          locationId: locationId,
          itemId: letterInfo.itemId
        }
      })

      if (!userItem) {
        userItem = new Game3UserItems()
        userItem.userId = user.id
        userItem.gameId = gameId
        userItem.locationId = locationId
        userItem.itemId = letterInfo.itemId
        userItem.quantity = 1
        userItem.used = 0
        userItem.createdAt = getNowGMT7()
      } else {
        userItem.quantity += 1
      }

      userItem.updatedAt = getNowGMT7()
      await userItemsRepo().save(userItem)
    }
  }

  // Trừ 1 lượt chơi (ưu tiên trừ inviteCount trước)
  if (userSpin.inviteCount > 0) {
    userSpin.inviteCount -= 1
  } else if (userSpin.spinCounts > 0) {
    userSpin.spinCounts -= 1
  }
  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  // Đánh dấu session đã hoàn thành
  playSession.status = Game3SessionStatus.COMPLETED
  playSession.updatedAt = getNowGMT7()
  await playSessionRepo().save(playSession)

  // Lấy tổng items hiện tại của user cho location này
  const userItems = await userItemsRepo().find({
    where: {
      userId: user.id,
      gameId,
      locationId: locationId
    },
    relations: ['itemsGame']
  })

  // Tính sao hiện tại
  const currentStars = calculateStars(userItems, locationRequirements)
  const remainingTurns = userSpin.spinCounts + (userSpin.inviteCount || 0)

  // Logic phát quà theo star (1 sao = 1 quà, 2 sao = 2 quà, 3 sao = 3 quà)
  const newRewards: Array<{
    type: string
    star_count: number | null
    reward_id: number
    name: string
    voucher: {
      biz_coupon_id: number | null
      code: string | null
      link: string | null
      name: string | null
      expires_at: string | null
    }
  }> = []
  let locationReward = null

  // Kiểm tra từng mức sao và phát quà tương ứng
  for (let starLevel = 1; starLevel <= currentStars; starLevel++) {
    // Kiểm tra đã nhận quà cho mức sao này chưa
    const existingRewardForStar = await userRewardsRepo().findOne({
      where: {
        userId: user.id,
        gameId,
        rewardType: 'location_items',
        locationId: locationId,
        starCount: starLevel
      }
    })

    if (!existingRewardForStar) {
      // Lấy prizes theo location và context = số sao
      const prizes = await getLocationPrizesWithRemainingQuantity(campaignId, gameId, locationId, starLevel)

      if (prizes.length > 0) {
        // Random prize theo winrate
        const selectedPrize = randomPrizeWithQuantityCheck(prizes)

        if (selectedPrize) {
          // Tạo reward record
          const reward = new GameUserRewards()
          reward.userId = user.id
          reward.gameId = gameId
          reward.rewardType = 'location_items'
          reward.locationId = locationId
          reward.starCount = starLevel
          reward.prizeId = selectedPrize.id
          reward.claimedAt = getNowGMT7()
          reward.createdAt = getNowGMT7()
          reward.updatedAt = getNowGMT7()

          // Gọi processCouponFromBizfly để lấy thông tin coupon
          const couponResult = await processCouponFromBizfly(user, selectedPrize, game, campaignId, gameId)

          if (couponResult.success && couponResult.couponData) {
            reward.bizCouponId = couponResult.couponData.couponId
            reward.voucherCode = couponResult.couponData.couponCode
            reward.voucherLink = couponResult.couponData.qrCodeLink
            reward.voucherName = couponResult.couponData.couponName
            reward.voucherExpried = couponResult.couponData.end_date
          }

          await userRewardsRepo().save(reward)

          // Lưu vào SpinHistory để tracking
          await luckyWheelService.saveSpinHistory(
            user.id,
            selectedPrize.id,
            campaignId,
            gameId,
            `location_${locationId} - stars: ${starLevel}`,
            locationId,
            couponResult.couponData || null
          )

          newRewards.push({
            type: 'location_items',
            star_count: starLevel,
            reward_id: selectedPrize.id,
            name: reward.voucherName || selectedPrize.name,
            voucher: {
              biz_coupon_id: reward.bizCouponId,
              code: reward.voucherCode,
              link: reward.voucherLink,
              name: reward.voucherName,
              expires_at: reward.voucherExpried
            }
          })

          // Set locationReward cho response (tương thích với logic cũ)
          if (starLevel === currentStars) {
            locationReward = {
              prize: selectedPrize,
              coupon: couponResult.couponData || null,
              message: couponResult.message
            }
          }
        }
      }
    }
  }

  // Kiểm tra và phát quà chữ cái TOKYOLIFE
  const allUserItemsForLetters = await userItemsRepo().find({
    where: {
      userId: user.id,
      gameId
    },
    relations: ['itemsGame']
  })

  const isTokyoLifeComplete = checkTokyoLifeCompletion(allUserItemsForLetters)
  let tokyoLifeReward = null

  // Kiểm tra đã nhận quà chữ cái chưa (location_id = 0 để đảm bảo chưa phát quà TOKYOLIFE)
  const existingLetterReward = await userRewardsRepo().findOne({
    where: {
      userId: user.id,
      gameId,
      rewardType: 'complete_letters',
      locationId: 0
    }
  })

  if (isTokyoLifeComplete && !existingLetterReward) {
    // Lấy prizes từ LuckyPrize với context = "4" và gameId = 29
    const prizes = await getPrizesWithRemainingQuantity(campaignId, gameId, '4')

    if (prizes.length > 0) {
      // Random prize theo winrate
      const selectedPrize = randomPrizeWithQuantityCheck(prizes)

      if (selectedPrize) {
        // Tạo reward record
        const letterReward = new GameUserRewards()
        letterReward.userId = user.id
        letterReward.gameId = gameId
        letterReward.rewardType = 'complete_letters'
        letterReward.locationId = 0 // location_id = 0 cho TOKYOLIFE reward
        letterReward.starCount = null
        letterReward.prizeId = selectedPrize.id
        letterReward.claimedAt = getNowGMT7()
        letterReward.createdAt = getNowGMT7()
        letterReward.updatedAt = getNowGMT7()

        // Gọi processCouponFromBizfly để lấy thông tin coupon
        const couponResult = await processCouponFromBizfly(user, selectedPrize, game, campaignId, gameId)

        if (couponResult.success && couponResult.couponData) {
          letterReward.bizCouponId = couponResult.couponData.couponId
          letterReward.voucherCode = couponResult.couponData.couponCode
          letterReward.voucherLink = couponResult.couponData.qrCodeLink
          letterReward.voucherName = couponResult.couponData.couponName
          letterReward.voucherExpried = couponResult.couponData.end_date
        }

        await userRewardsRepo().save(letterReward)

        // Lưu vào SpinHistory để tracking
        await luckyWheelService.saveSpinHistory(
          user.id,
          selectedPrize.id,
          campaignId,
          gameId,
          `location_${locationId} - tokyo_life_completed`,
          locationId,
          couponResult.couponData || null
        )

        newRewards.push({
          type: 'complete_letters',
          star_count: null,
          reward_id: selectedPrize.id,
          name: letterReward.voucherName || selectedPrize.name,
          voucher: {
            biz_coupon_id: letterReward.bizCouponId,
            code: letterReward.voucherCode,
            link: letterReward.voucherLink,
            name: letterReward.voucherName,
            expires_at: letterReward.voucherExpried
          }
        })

        // Set tokyoLifeReward cho response
        tokyoLifeReward = {
          prize: selectedPrize,
          coupon: couponResult.couponData || null,
          message: couponResult.message
        }
      }
    }
  }

  // Các biến cần thiết cho response
  const stars = currentStars
  const isLocationCompleted = currentStars >= 3 // 3 sao = hoàn thành
  const isTokyoLifeCompleted = isTokyoLifeComplete

  // Tính thông tin items và letters để trả về
  const totalItems: Record<string, number> = {}
  const collectedLettersFromItems: string[] = []

  userItems.forEach(item => {
    if (item.itemsGame && item.itemsGame.type === 'letter' && item.itemsGame.name) {
      const letter = item.itemsGame.name
      for (let i = 0; i < item.quantity; i++) {
        collectedLettersFromItems.push(letter)
      }
    } else if (item.itemsGame && item.itemsGame.type) {
      totalItems[item.itemsGame.type] = item.quantity
    }
  })

  return {
    status: 'success',
    play_turns: remainingTurns,
    time_left: duration,
    distance_covered: distance,
    collected_items: totalItems,
    collected_letters: collectedLettersFromItems,
    stars: stars,
    location_completed: isLocationCompleted,
    tokyo_life_completed: isTokyoLifeCompleted,
    rewards: {
      location_reward: locationReward,
      tokyo_life_reward: tokyoLifeReward
    },
    new_rewards: newRewards // Thêm thông tin về quà mới nhận được
  }
}
