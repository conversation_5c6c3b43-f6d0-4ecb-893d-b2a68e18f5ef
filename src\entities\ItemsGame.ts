import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('items_game', { schema: 'events' })
export class ItemsGame extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('varchar', { name: 'type', length: 50, nullable: false })
  type: string | null

  @Column('varchar', { name: 'name', length: 255, nullable: false })
  name: string | null

  @Column('timestamp', { name: 'created_at', nullable: true })
  createdAt: Date | null
}



